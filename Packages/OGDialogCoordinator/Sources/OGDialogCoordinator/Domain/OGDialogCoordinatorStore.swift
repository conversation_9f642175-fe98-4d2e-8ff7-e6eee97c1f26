import Combine
import NotificationCenter
@preconcurrency import OGAppKitSDK
import OGCore
import OGDIService
import OGDomainStore
import OGFeatureCore
import OGRouter
import OGScreenViewUpdate
import OGStorage
import OGSystemKit
import OGWebBridge

public typealias OGDialogCoordinatorStore = OGDomainStore<OGDialogCoordinatorState, OGDialogCoordinatorAction>

extension OGDomainStore where State == OGDialogCoordinatorState, Action == OGDialogCoordinatorAction {
  public static func make() -> OGDialogCoordinatorStore {
    OGDialogCoordinatorStore(
      reducer: OGDialogCoordinatorState.Reducer.reduce,
      middlewares: OGDialogCoordinatorMiddleware(),
      connector: OGDialogCoordinatorConnector()
    )
  }
}

// MARK: - OGDialogCoordinatorAction

public enum OGDialogCoordinatorAction: OGDomainAction, Equatable {
  case _update(debounceMs: Int64, behaviorsJson: String, pushOptInGranted: Bool)
  case _receivedWebBridge(String)
  case _navigate(String)
  case _receivedScreenView(URL)

  public static func == (lhs: OGDialogCoordinatorAction, rhs: OGDialogCoordinatorAction) -> Bool {
    switch (lhs, rhs) {
    case let (._update(lhsDebounce, lhsBehaviors, lhsPush), ._update(rhsDebounce, rhsBehaviors, rhsPush)):
      return lhsDebounce == rhsDebounce && lhsBehaviors == rhsBehaviors && lhsPush == rhsPush
    case let (._navigate(lhs), ._navigate(rhs)):
      return lhs == rhs
    case let (._receivedScreenView(lhsUrl), ._receivedScreenView(rhsUrl)):
      return lhsUrl == rhsUrl
    case let (._receivedWebBridge(lhsName), ._receivedWebBridge(rhsName)):
      return lhsName == rhsName
    default:
      return false
    }
  }
}

// MARK: - OGDialogCoordinatorState

public struct OGDialogCoordinatorState: OGDomainState, Equatable {
  public private(set) var isAwaitingUpdate: Bool

  public init(
    isAwaitingUpdate: Bool = false
  ) {
    self.isAwaitingUpdate = isAwaitingUpdate
  }

  public static let initial: Self = .init()
}

// MARK: OGDialogCoordinatorState.Reducer

extension OGDialogCoordinatorState {
  public enum Reducer: OGDomainActionReducible {
    public static func reduce(
      _: inout OGDialogCoordinatorState,
      with action: OGDialogCoordinatorAction
    ) {
      switch action {
      case ._navigate, ._receivedScreenView, ._receivedWebBridge, ._update:
        break
      }
    }
  }
}

// MARK: - OGDialogCoordinatorMiddleware

public struct OGDialogCoordinatorMiddleware: OGDomainMiddleware {
  @OGInjected(\OGRoutingContainer.routePublisher) private var routePublisher

  @OGInjected(\OGCoreContainer.coordinator) private var coordinator

  public func callAsFunction(
    action: OGDialogCoordinatorAction,
    for _: OGDialogCoordinatorState
  ) async throws
    -> OGDialogCoordinatorAction? {
    switch action {
    case let ._update(debounceMs, behaviorsJson, pushOptInGranted):

      coordinator.configure(debounceMs: debounceMs, behaviorsJson: behaviorsJson, pushOptInGranted: pushOptInGranted)
      return nil

    case let ._receivedWebBridge(webBridgeCallName):
      coordinator.onEvent(event: OGAppKitSDK.CoordinatorEventWebBridgeCall(name: webBridgeCallName))
      return nil

    case let ._navigate(url):
      routePublisher.send(OGRoute(url))
      return nil

    case let ._receivedScreenView(url):
      coordinator.onEvent(event: OGAppKitSDK.CoordinatorEventScreenView(url: url.absoluteString))
      return nil
    }
  }
}

// MARK: - OGDialogCoordinatorConnector

actor OGDialogCoordinatorConnector: OGDomainConnector {
  private var cancelationTokens = Set<AnyCancellable>()
  @OGInjected(\OGDialogCoordinatorFeatureAdapterContainer.coordinator) private var feature
  @OGInjected(\OGDialogCoordinatorContainer.dialogCoordinator) private var dialogCoordinator
  @OGInjected(\OGScreenViewUpdateContainer.screenViewUpdate) private var screenViewUpdate
  @OGInjected(\OGCoreContainer.coordinator) private var coordinator
  @OGInjected(\OGWebBridgeContainer.globalWebBridge) private var globalWebBridge
  @OGInjected(\OGSystemKitContainer.userNotificationCenter) private var userNotificationCenter
  init() {}

  func configure(
    dispatch: @escaping (OGDialogCoordinatorAction) async -> Void
  ) async {
    Publishers.CombineLatest(
      feature.configuration.removeDuplicates(),
      userNotificationCenter.isPushOptInGranted.removeDuplicates()
    )
    .sink { [weak self] configuration, pushOptInGranted in
      guard let self else { return }
      Task {
        // Use the pre-computed webBridgeNames from the configuration
        await self.dialogCoordinator.addWebBridgeNames(names: configuration.webBridgeNames)

        // Use the pre-computed behaviorsJson from the configuration
        await dispatch(._update(
          debounceMs: Int64(configuration.debounceMs),
          behaviorsJson: configuration.behaviorsJson,
          pushOptInGranted: pushOptInGranted
        ))
      }
    }
    .store(in: &cancelationTokens)

    await observeWebBridgeEvents(dispatch)

    screenViewUpdate.update
      .compactMap { $0 }
      .sink { url in
        Task {
          await dispatch(._receivedScreenView(url))
        }
      }
      .store(in: &cancelationTokens)

    observeMultiplatformActions(dispatch)
  }

  private func observeWebBridgeEvents(_ dispatch: @escaping (OGDialogCoordinatorAction) async -> Void) async {
    let webBridgeActionHandler = await dialogCoordinator.webBridgeActionHandler
    webBridgeActionHandler.webBridgeCallName
      .compactMap { $0 }
      .sink { callName in
        Task {
          await dispatch(._receivedWebBridge(callName))
        }
      }
      .store(in: &cancelationTokens)
  }

  private func observeMultiplatformActions(_ dispatch: @escaping (OGDialogCoordinatorAction) async -> Void) {
    Task {
      for await action in coordinator.getActions() {
        switch onEnum(of: action) {
        case let .navigation(action):
          await dispatch(._navigate(action.url))
        }
      }
    }
  }
}
